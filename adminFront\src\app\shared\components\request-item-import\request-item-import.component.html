<nb-card class="request-item-import-dialog">
  <nb-card-header class="d-flex justify-content-between align-items-center">
    <h5 class="mb-0">需求項目匯入</h5>
    <button nbButton ghost size="small" (click)="close()">
      <nb-icon icon="close-outline"></nb-icon>
    </button>
  </nb-card-header>

  <nb-card-body>
    <!-- 搜尋區塊 -->
    <div class="search-section mb-4" *ngIf="currentStep === 1">
      <div class="row">
        <div class="form-group col-12 col-md-6">
          <label for="buildCase" class="label mb-2">建案</label>
          <nb-select [(ngModel)]="searchRequest.CBuildCaseID" 
                     (ngModelChange)="onBuildCaseChange()"
                     placeholder="請選擇建案">
            <nb-option *ngFor="let case of buildCaseList" [value]="case.cID">
              {{ case.CBuildCaseName }}
            </nb-option>
          </nb-select>
        </div>
        <div class="form-group col-12 col-md-6">
          <label for="houseType" class="label mb-2">房屋類型</label>
          <nb-select [(ngModel)]="searchRequest.CHouseType" 
                     multiple
                     placeholder="請選擇房屋類型">
            <nb-option *ngFor="let type of houseTypeOptions" [value]="type.value">
              {{ type.label }}
            </nb-option>
          </nb-select>
        </div>
      </div>
      <div class="row">
        <div class="form-group col-12 col-md-6">
          <label for="location" class="label mb-2">區域</label>
          <input type="text" 
                 nbInput 
                 id="location" 
                 placeholder="請輸入區域"
                 [(ngModel)]="searchRequest.CLocation">
        </div>
        <div class="form-group col-12 col-md-6">
          <label for="requirement" class="label mb-2">工程項目</label>
          <input type="text" 
                 nbInput 
                 id="requirement" 
                 placeholder="請輸入工程項目"
                 [(ngModel)]="searchRequest.CRequirement">
        </div>
      </div>
      <div class="row">
        <div class="col-12 text-right">
          <button class="btn btn-secondary me-2" (click)="resetSearch()">
            <nb-icon icon="refresh-outline"></nb-icon>
            重置
          </button>
          <button class="btn btn-primary" (click)="onSearch()">
            <nb-icon icon="search-outline"></nb-icon>
            搜尋
          </button>
        </div>
      </div>
      <hr class="my-3">
    </div>

    <!-- 進度指示器 -->
    <div class="progress-indicator mb-4">
      <div class="progress-steps d-flex justify-content-between">
        <div class="step" [class.active]="currentStep >= 1" [class.completed]="currentStep > 1">
          <div class="step-number">1</div>
          <div class="step-label">選擇項目</div>
        </div>
        <div class="step-connector" [class.active]="currentStep > 1"></div>
        <div class="step" [class.active]="currentStep >= 2">
          <div class="step-number">2</div>
          <div class="step-label">確認匯入</div>
        </div>
      </div>
      <div class="progress-text text-center mt-2">
        <small class="text-muted">{{ getProgressText() }}</small>
      </div>
    </div>

    <!-- 載入中 -->
    <div *ngIf="loading" class="text-center py-4">
      <div class="spinner-border text-primary" role="status">
        <span class="sr-only">載入中...</span>
      </div>
      <div class="mt-2">載入需求項目中...</div>
    </div>

    <!-- 步驟1：選擇需求項目 -->
    <div *ngIf="currentStep === 1 && !loading">
      <div class="selection-header d-flex justify-content-between align-items-center mb-3">
        <h6 class="mb-0">可匯入的需求項目</h6>
        <div class="selection-actions">
          <button nbButton ghost size="small" (click)="selectAll()" class="me-2">
            <nb-icon icon="checkmark-square-2-outline"></nb-icon>
            全選/全消
          </button>
          <span class="text-muted">已選擇 {{ getSelectedCount() }} / {{ getTotalCount() }} 項</span>
        </div>
      </div>

      <div class="requirement-list" style="max-height: 400px; overflow-y: auto;">
        <div *ngIf="requirements.length === 0" class="text-center py-4 text-muted">
          <nb-icon icon="inbox-outline" class="mb-2" style="font-size: 2rem;"></nb-icon>
          <div>沒有可匯入的需求項目</div>
        </div>

        <div *ngFor="let requirement of requirements" class="requirement-item mb-2">
          <nb-card size="small">
            <nb-card-body class="py-2">
              <div class="d-flex align-items-center">
                <nb-checkbox 
                  [(ngModel)]="requirement.selected" 
                  (ngModelChange)="onRequirementItemChange()"
                  class="me-3">
                </nb-checkbox>
                
                <div class="requirement-info flex-grow-1">
                  <div class="requirement-name fw-bold">{{ requirement.CRequirement || '未命名需求' }}</div>
                  <div class="requirement-details small text-muted">
                    <span *ngIf="requirement.CLocation" class="me-3">
                      <nb-icon icon="pin-outline"></nb-icon>
                      位置: {{ requirement.CLocation }}
                    </span>
                    <span *ngIf="requirement.CUnit" class="me-3">
                      <nb-icon icon="cube-outline"></nb-icon>
                      單位: {{ requirement.CUnit }}
                    </span>
                    <span *ngIf="requirement.CUnitPrice" class="me-3">
                      <nb-icon icon="pricetags-outline"></nb-icon>
                      單價: {{ requirement.CUnitPrice | currency:'TWD':'symbol-narrow':'1.0-0' }}
                    </span>
                  </div>
                  <div *ngIf="requirement.CRemark" class="requirement-remark small text-info mt-1">
                    備註: {{ requirement.CRemark }}
                  </div>
                </div>

                <div class="requirement-status">
                  <span class="badge" [ngClass]="requirement.CIsShow ? 'badge-success' : 'badge-secondary'">
                    {{ requirement.CIsShow ? '顯示' : '隱藏' }}
                  </span>
                  
                  <span 
                    *ngIf="requirement.CIsSimple" 
                    class="badge badge-info ms-1">
                    簡化
                  </span>
                </div>
              </div>
            </nb-card-body>
          </nb-card>
        </div>
      </div>
    </div>

    <!-- 步驟2：確認匯入詳情 -->
    <div *ngIf="currentStep === 2">
      <h6 class="mb-3">匯入確認</h6>
      
      <div class="import-summary mb-4">
        <nb-card status="info" size="small">
          <nb-card-body>
            <div class="d-flex align-items-center">
              <nb-icon icon="info-outline" class="me-2"></nb-icon>
              <div>
                <div class="fw-bold">即將匯入 {{ getSelectedCount() }} 個需求項目</div>
                <div class="small text-muted">這些項目將被加入到目前的建案中</div>
              </div>
            </div>
          </nb-card-body>
        </nb-card>
      </div>

      <div class="selected-items-preview" style="max-height: 300px; overflow-y: auto;">
        <div *ngFor="let item of getSelectedItems(); let i = index" class="selected-item mb-2">
          <nb-card size="small">
            <nb-card-body class="py-2">
              <div class="d-flex align-items-center">
                <div class="item-number me-3">
                  <span class="badge badge-primary">{{ i + 1 }}</span>
                </div>
                <div class="item-info flex-grow-1">
                  <div class="item-name fw-bold">{{ item.CRequirement || '未命名需求' }}</div>
                  <div class="item-details small text-muted">
                    <span *ngIf="item.CLocation" class="me-3">位置: {{ item.CLocation }}</span>
                    <span *ngIf="item.CUnit" class="me-3">單位: {{ item.CUnit }}</span>
                    <span *ngIf="item.CUnitPrice">單價: {{ item.CUnitPrice | currency:'TWD':'symbol-narrow':'1.0-0' }}</span>
                  </div>
                </div>
              </div>
            </nb-card-body>
          </nb-card>
        </div>
      </div>
    </div>
  </nb-card-body>

  <nb-card-footer class="d-flex justify-content-between">
    <div class="left-actions">
      <button 
        *ngIf="currentStep > 1" 
        nbButton 
        ghost 
        (click)="previousStep()">
        <nb-icon icon="arrow-back-outline"></nb-icon>
        上一步
      </button>
    </div>

    <div class="right-actions">
      <button nbButton ghost (click)="close()" class="me-2">
        取消
      </button>
      
      <button 
        *ngIf="currentStep < 2" 
        nbButton 
        status="primary" 
        [disabled]="!canProceed()" 
        (click)="nextStep()">
        下一步
        <nb-icon icon="arrow-forward-outline"></nb-icon>
      </button>
      
      <button 
        *ngIf="currentStep === 2" 
        nbButton 
        status="success" 
        (click)="importRequirements()">
        <nb-icon icon="download-outline"></nb-icon>
        確認匯入
      </button>
    </div>
  </nb-card-footer>
</nb-card>