// Bootstrap badge styles
.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;

  &.badge-primary {
    color: #fff;
    background-color: #007bff;
  }

  &.badge-success {
    color: #fff;
    background-color: #28a745;
  }

  &.badge-info {
    color: #fff;
    background-color: #17a2b8;
  }

  &.badge-secondary {
    color: #fff;
    background-color: #6c757d;
  }
}

.request-item-import-dialog {
  min-width: 700px;
  max-width: 900px;
  min-height: 600px;
}

// 搜尋區塊樣式
.search-section {
  background-color: var(--nb-color-basic-100);
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid var(--nb-color-basic-300);

  .form-group {
    margin-bottom: 1rem;

    .label {
      font-weight: 500;
      color: var(--nb-color-text-basic);
      display: block;
    }

    nb-select, input {
      width: 100%;
    }
  }

  .btn {
    min-width: 80px;
    
    nb-icon {
      margin-right: 0.5rem;
    }
  }

  hr {
    border-color: var(--nb-color-basic-400);
    margin: 1rem 0;
  }
}

.progress-indicator {
  .progress-steps {
    .step {
      text-align: center;
      position: relative;
      flex: 1;

      .step-number {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: var(--nb-color-basic-400);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 8px;
        font-weight: bold;
        transition: all 0.3s ease;
      }

      .step-label {
        font-size: 0.875rem;
        color: var(--nb-color-text-hint);
        transition: color 0.3s ease;
      }

      &.active {
        .step-number {
          background-color: var(--nb-color-primary-500);
        }
        .step-label {
          color: var(--nb-color-text-basic);
          font-weight: 500;
        }
      }

      &.completed {
        .step-number {
          background-color: var(--nb-color-success-500);
        }
      }
    }

    .step-connector {
      height: 2px;
      background-color: var(--nb-color-basic-400);
      margin-top: 16px;
      transition: background-color 0.3s ease;

      &.active {
        background-color: var(--nb-color-primary-500);
      }
    }
  }
}

.selection-header {
  border-bottom: 1px solid var(--nb-color-basic-300);
  padding-bottom: 12px;
}

.requirement-list {
  .requirement-item {
    .requirement-info {
      .requirement-name {
        color: var(--nb-color-text-basic);
        margin-bottom: 4px;
      }

      .requirement-details {
        display: flex;
        flex-wrap: wrap;
        
        span {
          display: flex;
          align-items: center;
          
          nb-icon {
            margin-right: 4px;
            font-size: 0.875rem;
          }
        }
      }

      .requirement-remark {
        color: var(--nb-color-info-600);
        font-style: italic;
      }
    }

    .requirement-status {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 4px;
    }

    &:hover {
      nb-card {
        border-color: var(--nb-color-primary-300);
      }
    }
  }
}

.import-summary {
  nb-card {
    background-color: var(--nb-color-info-100);
    border-color: var(--nb-color-info-300);
  }
}

.selected-items-preview {
  .selected-item {
    .item-number {
      .badge {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .item-info {
      .item-name {
        color: var(--nb-color-text-basic);
        margin-bottom: 4px;
      }

      .item-details {
        color: var(--nb-color-text-hint);
      }
    }
  }
}

.left-actions, .right-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

// 響應式設計
@media (max-width: 768px) {
  .request-item-import-dialog {
    min-width: 95vw;
    max-width: 95vw;
  }

  .requirement-details {
    flex-direction: column !important;
    
    span {
      margin-bottom: 2px !important;
    }
  }

  .selection-header {
    flex-direction: column;
    gap: 12px;
    
    .selection-actions {
      justify-content: space-between;
      width: 100%;
    }
  }
}

// 深色主題適配
:host-context(.dark) {
  .progress-indicator {
    .progress-steps {
      .step {
        .step-number {
          background-color: var(--nb-color-basic-600);
        }
      }
      
      .step-connector {
        background-color: var(--nb-color-basic-600);
      }
    }
  }

  .selection-header {
    border-bottom-color: var(--nb-color-basic-600);
  }

  .import-summary {
    nb-card {
      background-color: var(--nb-color-info-200);
    }
  }
}